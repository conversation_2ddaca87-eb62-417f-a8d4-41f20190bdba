<template>
  <section class="flex flex-col w-full h-full overflow-hidden bg-n-background">
    <main class="flex-1 px-6 overflow-y-auto">
      <div class="w-full max-w-[60rem] mx-auto py-6">
        <div class="strategy-center">
          <!-- 页面标题 -->
          <div class="mb-6">
            <h1 class="text-2xl font-semibold text-n-slate-12">策略中心</h1>
            <p class="text-n-slate-11 mt-2">管理和配置您的客户策略</p>
          </div>

          <!-- 顶部工具栏 - 统一容器 -->
          <div class="branch-info">
            <!-- 用户选择器 -->
            <div class="user-selector">
              <select v-model="selectedUser" class="user-select">
                <option v-for="user in userOptions" :key="user.value" :value="user">
                  {{ user.name }}
                </option>
              </select>
            </div>

            <!-- 分支选择器 -->
            <GitHubBranchSelectorSimple
              :branches="branches"
              :tags="tags"
              :branch-stats="branchStats"
              @branch-changed="handleBranchChange"
              @tag-changed="handleTagChange"
              @view-all-branches="handleViewAllBranches"
            />
          </div>

          <!-- 策略表格 -->
          <div class="strategy-table-container">
            <div class="table-header">
              <div class="header-tabs">
                <div class="tab active">生命周期阶段</div>
                <div class="tab">触发</div>
                <div class="tab">目标</div>
                <div class="tab">策略</div>
                <div class="tab">负责人</div>
                <div class="tab">重要性</div>
                <div class="tab">约束条件</div>
                <div class="tab">操作</div>
              </div>
            </div>

            <div class="strategy-table">
              <div
                v-for="(row, index) in strategyRows"
                :key="row.id"
                class="table-row"
                :class="{ 'row-even': index % 2 === 1, 'editing': row.isEditing }"
              >
                <!-- 编辑模式 -->
                <template v-if="row.isEditing">
                  <div class="cell stage-cell">
                    <input v-model="row.stage" class="cell-input" placeholder="生命周期阶段" />
                  </div>
                  <div class="cell trigger-cell">
                    <input v-model="row.trigger" class="cell-input" placeholder="触发条件" />
                  </div>
                  <div class="cell target-cell">
                    <input v-model="row.target" class="cell-input" placeholder="目标" />
                  </div>
                  <div class="cell strategy-cell">
                    <textarea v-model="row.strategy" class="cell-textarea" placeholder="策略内容"></textarea>
                  </div>
                  <div class="cell agent-cell">
                    <select v-model="row.agent" class="cell-select">
                      <option value="智能客服">智能客服</option>
                      <option value="销售专员">销售专员</option>
                      <option value="技术支持">技术支持</option>
                      <option value="客户经理">客户经理</option>
                    </select>
                  </div>
                  <div class="cell importance-cell">
                    <select v-model="row.importance" class="cell-select">
                      <option value="高">高</option>
                      <option value="中">中</option>
                      <option value="低">低</option>
                    </select>
                  </div>
                  <div class="cell constraints-cell">
                    <input v-model="row.constraints" class="cell-input" placeholder="约束条件" />
                  </div>
                  <div class="cell actions-cell">
                    <button @click="saveRow(row)" class="action-btn save-btn">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="20,6 9,17 4,12"></polyline>
                      </svg>
                    </button>
                    <button @click="cancelEdit(row)" class="action-btn cancel-btn">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </button>
                  </div>
                </template>

                <!-- 显示模式 -->
                <template v-else>
                  <div class="cell stage-cell">{{ row.stage }}</div>
                  <div class="cell trigger-cell">{{ row.trigger }}</div>
                  <div class="cell target-cell">{{ row.target }}</div>
                  <div class="cell strategy-cell" v-html="row.strategy"></div>
                  <div class="cell agent-cell">
                    <span class="agent-badge">{{ row.agent }}</span>
                  </div>
                  <div class="cell importance-cell">
                    <span class="importance-badge" :class="getImportanceClass(row.importance)">{{ row.importance }}</span>
                  </div>
                  <div class="cell constraints-cell">{{ row.constraints }}</div>
                  <div class="cell actions-cell">
                    <button @click="editRow(row)" class="action-btn edit-btn">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                      </svg>
                    </button>
                    <button @click="deleteRow(row)" class="action-btn delete-btn">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="3,6 5,6 21,6"></polyline>
                        <path d="M19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"></path>
                      </svg>
                    </button>
                  </div>
                </template>
              </div>

              <!-- 添加新行按钮 -->
              <div class="add-row-container">
                <button class="add-row-btn" @click="handleAddRow">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                  新增策略
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </section>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import GitHubBranchSelectorSimple from './components/GitHubBranchSelectorSimple.vue';

const { t } = useI18n();

// 模拟数据
const userOptions = computed(() => [
  { value: 1, label: 'Wade Cooper', name: 'Wade Cooper' },
  { value: 2, label: 'Arlene Mccoy', name: 'Arlene Mccoy' },
  { value: 3, label: 'Devon Webb', name: 'Devon Webb' }
]);

const selectedUser = ref({ value: 1, label: 'Wade Cooper', name: 'Wade Cooper' });

const branches = computed(() => [
  { id: 1, name: 'master', isDefault: true },
  { id: 2, name: 'develop', isDefault: false },
  { id: 3, name: 'feature/strategy-v2', isDefault: false },
]);

const tags = computed(() => [
  { id: 1, name: 'v2.1.0', version: 'Latest' },
  { id: 2, name: 'v2.0.5', version: '' },
]);

const selectedBranch = ref({ id: 1, name: 'master', isDefault: true });

const branchStats = ref({
  branchCount: 197,
  tagCount: 341
});

// 策略表格数据
const strategyRows = ref([
  {
    id: 1,
    stage: '新访客',
    trigger: '首次访问',
    target: '提高转化率',
    strategy: '欢迎消息 + 产品介绍',
    agent: '智能客服',
    importance: '高',
    constraints: '工作时间内',
    isEditing: false,
    originalData: null
  },
  {
    id: 2,
    stage: '潜在客户',
    trigger: '浏览产品页面',
    target: '促进购买',
    strategy: '个性化推荐',
    agent: '销售专员',
    importance: '中',
    constraints: '有库存时',
    isEditing: false,
    originalData: null
  },
  {
    id: 3,
    stage: '活跃用户',
    trigger: '连续使用7天',
    target: '提升留存率',
    strategy: '发送使用技巧和高级功能介绍',
    agent: '客户成功',
    importance: '高',
    constraints: '用户活跃度>80%',
    isEditing: false,
    originalData: null
  },
  {
    id: 4,
    stage: '流失预警',
    trigger: '7天未登录',
    target: '挽回用户',
    strategy: '发送优惠券和新功能通知',
    agent: '客户经理',
    importance: '高',
    constraints: '付费用户优先',
    isEditing: false,
    originalData: null
  }
]);

// 事件处理
const handleUserChange = (user) => {
  console.log('User changed:', user);
};

const handleBranchChange = (branch) => {
  console.log('Branch changed:', branch);
};

const handleTagChange = (tag) => {
  console.log('Tag changed:', tag);
};

const handleViewAllBranches = () => {
  console.log('View all branches clicked');
};

const handleAddRow = () => {
  const newRow = {
    id: Date.now(),
    stage: '新阶段',
    trigger: '',
    target: '',
    strategy: '',
    agent: '智能客服',
    importance: '中',
    constraints: '',
    isEditing: true,
    originalData: null
  };
  strategyRows.value.push(newRow);
};

// 编辑功能
const editRow = (row) => {
  // 保存原始数据用于取消编辑
  row.originalData = { ...row };
  row.isEditing = true;
};

const saveRow = (row) => {
  // 这里可以添加保存到后端的逻辑
  row.isEditing = false;
  row.originalData = null;
  console.log('保存策略:', row);
};

const cancelEdit = (row) => {
  if (row.originalData) {
    // 恢复原始数据
    Object.assign(row, row.originalData);
    row.originalData = null;
  }
  row.isEditing = false;
};

const deleteRow = (row) => {
  if (confirm('确定要删除这条策略吗？')) {
    const index = strategyRows.value.findIndex(r => r.id === row.id);
    if (index > -1) {
      strategyRows.value.splice(index, 1);
    }
  }
};

// 重要性样式
const getImportanceClass = (importance) => {
  switch (importance) {
    case '高':
      return 'importance-high';
    case '中':
      return 'importance-medium';
    case '低':
      return 'importance-low';
    default:
      return '';
  }
};
</script>

<style scoped>
.strategy-center {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 顶部工具栏 */
.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 用户选择器 */
.user-selector {
  display: flex;
  align-items: center;
}

.user-select {
  padding: 8px 12px;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 150px;
  cursor: pointer;
}

.user-select:hover {
  border-color: #9ca3af;
}

.user-select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* 分支信息栏 - 统一容器 */
.branch-info {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  gap: 16px;
  flex-wrap: wrap;
}

/* 策略表格容器 */
.strategy-table-container {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  padding: 12px 16px;
}

.header-tabs {
  display: flex;
  gap: 24px;
}

.tab {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
}

.tab.active {
  color: #1f2937;
  border-bottom: 2px solid #3b82f6;
}

/* 策略表格 */
.strategy-table {
  max-height: 600px;
  overflow-y: auto;
  min-width: 800px;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 2fr 1fr 1fr 1fr 120px;
  border-bottom: 1px solid #e5e7eb;
}

.table-row.editing {
  background: #fef3c7;
}

.cell {
  padding: 12px 16px;
  font-size: 14px;
  border-right: 1px solid #e5e7eb;
}

.cell:last-child {
  border-right: none;
}

.agent-badge,
.importance-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background: #e0f2fe;
  color: #0369a1;
}

.importance-badge.importance-high {
  background: #fee2e2;
  color: #dc2626;
}

.importance-badge.importance-medium {
  background: #fef3c7;
  color: #d97706;
}

.importance-badge.importance-low {
  background: #dcfce7;
  color: #16a34a;
}

/* 编辑控件样式 */
.cell-input,
.cell-select {
  width: 100%;
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

.cell-textarea {
  width: 100%;
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  resize: vertical;
  min-height: 60px;
}

.cell-input:focus,
.cell-select:focus,
.cell-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

/* 操作按钮 */
.actions-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.edit-btn {
  background: #f3f4f6;
  color: #6b7280;
}

.edit-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.delete-btn {
  background: #fef2f2;
  color: #dc2626;
}

.delete-btn:hover {
  background: #fee2e2;
  color: #b91c1c;
}

.save-btn {
  background: #f0fdf4;
  color: #16a34a;
}

.save-btn:hover {
  background: #dcfce7;
  color: #15803d;
}

.cancel-btn {
  background: #fef2f2;
  color: #dc2626;
}

.cancel-btn:hover {
  background: #fee2e2;
  color: #b91c1c;
}

.add-row-container {
  padding: 16px;
  text-align: center;
  border-top: 1px solid #e5e7eb;
}

.add-row-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
}

.add-row-btn:hover {
  background: #e5e7eb;
}

/* 响应式 */
@media (max-width: 768px) {
  .strategy-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-left {
    width: 100%;
    justify-content: space-between;
  }
  
  .strategy-table-container {
    overflow-x: auto;
  }
  
  .strategy-table {
    min-width: 1200px;
  }

  .table-row {
    grid-template-columns: 1fr 1fr 1fr 1.5fr 1fr 1fr 1fr 100px;
  }

  .actions-cell {
    flex-direction: column;
    gap: 4px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
  }
}
</style>
