<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import Button from 'dashboard/components-next/button/Button.vue';

const { t } = useI18n();

const props = defineProps({
  selectedUser: {
    type: Object,
    required: true
  },
  userOptions: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(['user-change', 'create-task']);

const selectedUserOption = computed({
  get() {
    return props.userOptions.find(option => option.value === props.selectedUser?.id) || null;
  },
  set(option) {
    if (option) {
      const user = {
        id: option.value,
        name: option.label
      };
      emit('user-change', user);
    }
  }
});

const handleUserChange = (event) => {
  const selectedOption = event.target.value;
  if (selectedOption && selectedOption.value) {
    const user = {
      id: selectedOption.value,
      name: selectedOption.label
    };
    emit('user-change', user);
  }
};

const handleCreateTask = () => {
  emit('create-task');
};
</script>

<template>
  <div class="flex items-center justify-between p-4 mb-4 bg-white rounded-lg border border-n-weak shadow-sm">
    <!-- 左侧：用户选择器 -->
    <div class="flex items-center gap-4">
      <select
        v-model="selectedUserOption"
        class="min-w-48 p-2 text-sm border border-n-weak rounded focus:outline-none focus:ring-2 focus:ring-n-brand/20 bg-white"
        @change="handleUserChange"
      >
        <option value="">{{ selectedUser?.name || 'Select User' }}</option>
        <option
          v-for="option in userOptions"
          :key="option.value"
          :value="option"
        >
          {{ option.label }}
        </option>
      </select>
    </div>
    
    <!-- 右侧：操作按钮 -->
    <div class="flex items-center gap-2">
      <Button
        variant="solid"
        color="blue"
        size="sm"
        @click="handleCreateTask"
      >
        新建任务
      </Button>
    </div>
  </div>
</template>

<style scoped>
/* 工具栏样式 */
.toolbar-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border: 1px solid var(--n-weak);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar-container {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .toolbar-container > div {
    justify-content: center;
  }
}
</style>
