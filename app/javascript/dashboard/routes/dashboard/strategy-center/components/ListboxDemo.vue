<template>
  <div class="listbox-demo p-6 space-y-6">
    <h2 class="text-xl font-semibold mb-4">Headless UI Listbox 演示</h2>
    
    <!-- 用户选择器演示 -->
    <div class="demo-section">
      <h3 class="text-lg font-medium mb-2">用户选择器</h3>
      <div class="w-64">
        <Listbox v-model="selectedUser">
          <div class="relative">
            <ListboxButton class="listbox-button">
              <span class="block truncate">{{ selectedUser.name }}</span>
              <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3z" clip-rule="evenodd" />
                </svg>
              </span>
            </ListboxButton>

            <transition
              leave-active-class="transition duration-100 ease-in"
              leave-from-class="opacity-100"
              leave-to-class="opacity-0"
            >
              <ListboxOptions class="listbox-options">
                <ListboxOption
                  v-slot="{ active, selected }"
                  v-for="user in users"
                  :key="user.id"
                  :value="user"
                  as="template"
                >
                  <li
                    :class="[
                      active ? 'bg-blue-100 text-blue-900' : 'text-gray-900',
                      'relative cursor-default select-none py-2 pl-10 pr-4',
                    ]"
                  >
                    <span
                      :class="[
                        selected ? 'font-medium' : 'font-normal',
                        'block truncate',
                      ]"
                    >
                      {{ user.name }}
                    </span>
                    <span
                      v-if="selected"
                      class="absolute inset-y-0 left-0 flex items-center pl-3 text-blue-600"
                    >
                      <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                    </span>
                  </li>
                </ListboxOption>
              </ListboxOptions>
            </transition>
          </div>
        </Listbox>
      </div>
      <p class="text-sm text-gray-600 mt-2">当前选择: {{ selectedUser.name }}</p>
    </div>

    <!-- 分支选择器演示 -->
    <div class="demo-section">
      <h3 class="text-lg font-medium mb-2">分支选择器</h3>
      <div class="w-64">
        <Listbox v-model="selectedBranch">
          <div class="relative">
            <ListboxButton class="listbox-button">
              <div class="flex items-center gap-2">
                <svg class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <line x1="6" y1="3" x2="6" y2="15"></line>
                  <circle cx="18" cy="6" r="3"></circle>
                  <circle cx="6" cy="18" r="3"></circle>
                  <path d="m18 9a9 9 0 0 1-9 9"></path>
                </svg>
                <span class="block truncate">{{ selectedBranch.name }}</span>
                <span v-if="selectedBranch.isDefault" class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">default</span>
              </div>
              <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3z" clip-rule="evenodd" />
                </svg>
              </span>
            </ListboxButton>

            <transition
              leave-active-class="transition duration-100 ease-in"
              leave-from-class="opacity-100"
              leave-to-class="opacity-0"
            >
              <ListboxOptions class="listbox-options">
                <ListboxOption
                  v-slot="{ active, selected }"
                  v-for="branch in branches"
                  :key="branch.id"
                  :value="branch"
                  as="template"
                >
                  <li
                    :class="[
                      active ? 'bg-blue-100 text-blue-900' : 'text-gray-900',
                      'relative cursor-default select-none py-2 pl-10 pr-4',
                    ]"
                  >
                    <div class="flex items-center justify-between">
                      <span
                        :class="[
                          selected ? 'font-medium' : 'font-normal',
                          'block truncate',
                        ]"
                      >
                        {{ branch.name }}
                      </span>
                      <span v-if="branch.isDefault" class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded ml-2">default</span>
                    </div>
                    <span
                      v-if="selected"
                      class="absolute inset-y-0 left-0 flex items-center pl-3 text-blue-600"
                    >
                      <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                    </span>
                  </li>
                </ListboxOption>
              </ListboxOptions>
            </transition>
          </div>
        </Listbox>
      </div>
      <p class="text-sm text-gray-600 mt-2">当前选择: {{ selectedBranch.name }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import {
  Listbox,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from '@headlessui/vue';

const users = [
  { id: 1, name: 'Wade Cooper' },
  { id: 2, name: 'Arlene Mccoy' },
  { id: 3, name: 'Devon Webb' },
  { id: 4, name: 'Tom Cook' },
  { id: 5, name: 'Tanya Fox' },
];

const branches = [
  { id: 1, name: 'master', isDefault: true },
  { id: 2, name: 'develop', isDefault: false },
  { id: 3, name: 'feature/strategy-v2', isDefault: false },
  { id: 4, name: 'feature/user-management', isDefault: false },
  { id: 5, name: 'hotfix/login-issue', isDefault: false },
];

const selectedUser = ref(users[0]);
const selectedBranch = ref(branches[0]);
</script>

<style scoped>
.listbox-demo {
  max-width: 800px;
  margin: 0 auto;
}

.demo-section {
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
}

.listbox-button {
  relative w-full cursor-default rounded-lg bg-white py-2 pl-3 pr-10 text-left shadow-md focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm;
  @apply w-full cursor-pointer rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-left shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 sm:text-sm;
}

.listbox-options {
  @apply absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm;
}
</style>
