<template>
  <div class="simple-test">
    <h1>策略中心 - 简单测试页面</h1>
    <p>如果你能看到这个页面，说明路由正常工作。</p>
    <p>当前时间: {{ currentTime }}</p>
    <button @click="updateTime">更新时间</button>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const currentTime = ref(new Date().toLocaleString());

const updateTime = () => {
  currentTime.value = new Date().toLocaleString();
};
</script>

<style scoped>
.simple-test {
  padding: 2rem;
  font-family: Arial, sans-serif;
}

h1 {
  color: #333;
  margin-bottom: 1rem;
}

p {
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

button {
  padding: 0.5rem 1rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #0056b3;
}
</style>
