<template>
  <div class="github-branch-selector">
    <!-- 分支选择按钮 -->
    <button
      ref="triggerButton"
      class="branch-button"
      @click="toggleDropdown"
      :class="{ active: isOpen }"
    >
      <svg class="branch-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M9.5 3.25a2.25 2.25 0 1 1 3 2.122V6A2.5 2.5 0 0 1 10 8.5H6a1 1 0 0 0-1 1v1.128a2.251 2.251 0 1 1-1.5 0V5.372a2.25 2.25 0 1 1 1.5 0v1.836A2.493 2.493 0 0 1 6 7h4a1 1 0 0 0 1-1v-.628A2.25 2.25 0 0 1 9.5 3.25Zm-6 0a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0Zm8.25-.75a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5ZM4.25 12a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5Z"/>
      </svg>
      <span class="branch-name">{{ selectedBranch.name }}</span>
      <svg class="dropdown-arrow" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M4.427 7.427l3.396 3.396a.25.25 0 00.354 0l3.396-3.396A.25.25 0 0011.396 7H4.604a.25.25 0 00-.177.427z"/>
      </svg>
    </button>

    <!-- 下拉菜单 -->
    <transition
      enter-active-class="transition duration-200 ease-out"
      enter-from-class="transform scale-95 opacity-0"
      enter-to-class="transform scale-100 opacity-100"
      leave-active-class="transition duration-75 ease-in"
      leave-from-class="transform scale-100 opacity-100"
      leave-to-class="transform scale-95 opacity-0"
    >
      <div v-if="isOpen" class="dropdown-menu" @click.stop>
        <!-- 头部 -->
        <div class="dropdown-header">
          <h3 class="dropdown-title">Switch branches/tags</h3>
          <button class="close-button" @click="closeDropdown">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.75.75 0 1 1 1.06 1.06L9.06 8l3.22 3.22a.75.75 0 1 1-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 0 1-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06Z"/>
            </svg>
          </button>
        </div>

        <!-- 搜索框 -->
        <div class="search-container">
          <div class="search-input-wrapper">
            <svg class="search-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M10.68 11.74a6 6 0 0 1-7.922-8.982 6 6 0 0 1 8.982 7.922l3.04 3.04a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215ZM11.5 7a4.499 4.499 0 1 0-8.997 0A4.499 4.499 0 0 0 11.5 7Z"/>
            </svg>
            <input
              ref="searchInput"
              v-model="searchQuery"
              type="text"
              placeholder="Find a branch..."
              class="search-input"
              @input="handleSearch"
            />
          </div>
        </div>

        <!-- 标签页 -->
        <div class="tabs">
          <button
            class="tab"
            :class="{ active: activeTab === 'branches' }"
            @click="activeTab = 'branches'"
          >
            Branches
          </button>
          <button
            class="tab"
            :class="{ active: activeTab === 'tags' }"
            @click="activeTab = 'tags'"
          >
            Tags
          </button>
        </div>

        <!-- 列表内容 -->
        <div class="list-container">
          <div v-if="activeTab === 'branches'" class="branch-list">
            <div
              v-for="branch in filteredBranches"
              :key="branch.id"
              class="list-item"
              :class="{ selected: branch.id === selectedBranch.id }"
              @click="selectBranch(branch)"
            >
              <div class="item-content">
                <svg v-if="branch.id === selectedBranch.id" class="check-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                  <path d="M13.78 4.22a.75.75 0 0 1 0 1.06l-7.25 7.25a.75.75 0 0 1-1.06 0L2.22 9.28a.75.75 0 0 1 1.06-1.06L6 10.94l6.72-6.72a.75.75 0 0 1 1.06 0Z"/>
                </svg>
                <span class="item-name">{{ branch.name }}</span>
                <span v-if="branch.isDefault" class="default-badge">default</span>
              </div>
            </div>
          </div>

          <div v-else class="tag-list">
            <div
              v-for="tag in filteredTags"
              :key="tag.id"
              class="list-item"
              @click="selectTag(tag)"
            >
              <div class="item-content">
                <span class="item-name">{{ tag.name }}</span>
                <span class="tag-version">{{ tag.version }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="dropdown-footer">
          <button class="view-all-button" @click="viewAllBranches">
            View all branches
          </button>
        </div>
      </div>
    </transition>

    <!-- 遮罩层 -->
    <div v-if="isOpen" class="overlay" @click="closeDropdown"></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';

// Props
const props = defineProps({
  branches: {
    type: Array,
    default: () => [
      { id: 1, name: 'master', isDefault: true },
      { id: 2, name: 'develop', isDefault: false },
    ]
  },
  tags: {
    type: Array,
    default: () => [
      { id: 1, name: 'v2.1.0', version: 'Latest' },
      { id: 2, name: 'v2.0.5', version: '' },
    ]
  },
  branchStats: {
    type: Object,
    default: () => ({
      branchCount: 197,
      tagCount: 341
    })
  }
});

// Emits
const emit = defineEmits(['branch-changed', 'view-all-branches']);

// Reactive data
const isOpen = ref(false);
const searchQuery = ref('');
const activeTab = ref('branches');
const selectedBranch = ref(props.branches[0]);

// Refs
const triggerButton = ref(null);
const searchInput = ref(null);

// Computed
const filteredBranches = computed(() => {
  if (!searchQuery.value) return props.branches;
  return props.branches.filter(branch =>
    branch.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

const filteredTags = computed(() => {
  if (!searchQuery.value) return props.tags;
  return props.tags.filter(tag =>
    tag.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

// Methods
const toggleDropdown = () => {
  isOpen.value = !isOpen.value;
  if (isOpen.value) {
    nextTick(() => {
      searchInput.value?.focus();
    });
  }
};

const closeDropdown = () => {
  isOpen.value = false;
  searchQuery.value = '';
};

const selectBranch = (branch) => {
  selectedBranch.value = branch;
  emit('branch-changed', branch);
  closeDropdown();
};

const selectTag = (tag) => {
  // Handle tag selection
  console.log('Tag selected:', tag);
  closeDropdown();
};

const handleSearch = () => {
  // Search is handled by computed properties
};

const viewAllBranches = () => {
  emit('view-all-branches');
  closeDropdown();
};

// Handle click outside
const handleClickOutside = (event) => {
  if (isOpen.value && !triggerButton.value?.contains(event.target)) {
    closeDropdown();
  }
};

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.github-branch-selector {
  position: relative;
  display: inline-block;
}

/* 分支按钮样式 */

.branch-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f6f8fa;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  color: #24292f;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.branch-button:hover {
  background: #f3f4f6;
  border-color: #d0d7de;
}

.branch-button.active {
  background: #ffffff;
  border-color: #0969da;
  box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.12);
}

.branch-icon {
  color: #656d76;
  flex-shrink: 0;
}

.branch-name {
  flex: 1;
  text-align: left;
}

.dropdown-arrow {
  color: #656d76;
  flex-shrink: 0;
}



/* 下拉菜单 */
.dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  z-index: 1000;
  width: 320px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(100px);
  border: 1px solid #d0d7de;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

@media (prefers-color-scheme: dark) {
  .dropdown-menu {
    background: rgba(22, 27, 34, 0.95);
    border-color: #30363d;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);
  }
}

.dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 16px 8px 16px;
  border-bottom: 1px solid #d0d7de;
}

.dropdown-title {
  font-size: 14px;
  font-weight: 600;
  color: #24292f;
  margin: 0;
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  border-radius: 4px;
  color: #656d76;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: #f3f4f6;
  color: #24292f;
}

/* 搜索框 */
.search-container {
  padding: 8px 16px 16px 16px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #656d76;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 2px solid #0969da;
  border-radius: 6px;
  font-size: 14px;
  background: #ffffff;
  color: #24292f;
  outline: none;
  transition: all 0.2s ease;
}

.search-input::placeholder {
  color: #656d76;
}

.search-input:focus {
  border-color: #0969da;
  box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.12);
}

/* 标签页 */
.tabs {
  display: flex;
  border-bottom: 1px solid #d0d7de;
  margin: 0 16px;
}

.tab {
  flex: 1;
  padding: 8px 16px;
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  font-size: 14px;
  font-weight: 500;
  color: #656d76;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab:hover {
  color: #24292f;
}

.tab.active {
  color: #24292f;
  border-bottom-color: #fd8c73;
}

/* 列表容器 */
.list-container {
  max-height: 300px;
  overflow-y: auto;
  padding: 8px 0;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.list-item:hover {
  background: #f6f8fa;
}

.list-item.selected {
  background: #dbeafe;
}

.item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.check-icon {
  color: #0969da;
  flex-shrink: 0;
}

.item-name {
  flex: 1;
  font-size: 14px;
  color: #24292f;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

.default-badge {
  padding: 2px 6px;
  background: #dbeafe;
  color: #0969da;
  font-size: 12px;
  font-weight: 500;
  border-radius: 12px;
  border: 1px solid #0969da;
}

.tag-version {
  padding: 2px 6px;
  background: #f6f8fa;
  color: #656d76;
  font-size: 12px;
  border-radius: 4px;
}

/* 底部按钮 */
.dropdown-footer {
  padding: 8px 16px 16px 16px;
  border-top: 1px solid #d0d7de;
}

.view-all-button {
  width: 100%;
  padding: 8px 16px;
  background: transparent;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  color: #24292f;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-all-button:hover {
  background: #f6f8fa;
}

/* 遮罩层 */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}

/* 滚动条样式 */
.list-container::-webkit-scrollbar {
  width: 6px;
}

.list-container::-webkit-scrollbar-track {
  background: #f6f8fa;
}

.list-container::-webkit-scrollbar-thumb {
  background: #d0d7de;
  border-radius: 3px;
}

.list-container::-webkit-scrollbar-thumb:hover {
  background: #afb8c1;
}
</style>
