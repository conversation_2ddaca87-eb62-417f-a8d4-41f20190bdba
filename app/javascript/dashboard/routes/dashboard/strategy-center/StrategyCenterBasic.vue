<template>
  <div style="padding: 32px; background: white; min-height: 100vh; font-family: Arial, sans-serif;">
    <h1 style="font-size: 32px; font-weight: bold; color: #111; margin-bottom: 16px;">
      策略中心 - 基础测试
    </h1>
    <p style="color: #666; margin-bottom: 16px;">
      如果你能看到这个页面，说明路由正常工作。
    </p>
    <p style="font-size: 14px; color: #999;">
      当前时间: {{ currentTime }}
    </p>
    <button
      @click="updateTime"
      style="margin-top: 16px; padding: 8px 16px; background: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer;"
    >
      更新时间
    </button>

    <div style="margin-top: 32px; padding: 16px; background: #f3f4f6; border-radius: 8px;">
      <h2 style="font-size: 18px; margin-bottom: 8px;">调试信息</h2>
      <p>Vue 组件正常加载: ✅</p>
      <p>响应式数据正常: ✅</p>
      <p>事件处理正常: {{ clickCount > 0 ? '✅' : '❌' }}</p>
      <p>点击次数: {{ clickCount }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const currentTime = ref(new Date().toLocaleString());
const clickCount = ref(0);

const updateTime = () => {
  currentTime.value = new Date().toLocaleString();
  clickCount.value++;
};

// 页面加载时的调试信息
console.log('策略中心基础测试页面已加载');
console.log('当前路由:', window.location.pathname);
</script>
