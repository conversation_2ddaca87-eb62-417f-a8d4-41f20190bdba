<template>
  <div class="github-branch-selector">
    <!-- 主按钮区域 -->
    <div class="branch-header">
      <!-- 分支选择按钮 -->
      <button
        class="branch-button"
        @click="toggleDropdown"
        :class="{ active: isOpen }"
      >
        <svg class="branch-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          <path d="M9.5 3.25a2.25 2.25 0 1 1 3 2.122V6A2.5 2.5 0 0 1 10 8.5H6a1 1 0 0 0-1 1v1.128a2.251 2.251 0 1 1-1.5 0V5.372a2.25 2.25 0 1 1 1.5 0v1.836A2.493 2.493 0 0 1 6 7h4a1 1 0 0 0 1-1v-.628A2.25 2.25 0 0 1 9.5 3.25Zm-6 0a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0Zm8.25-.75a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5ZM4.25 12a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5Z"/>
        </svg>
        <span class="branch-name">{{ selectedBranch.name }}</span>
        <svg class="dropdown-arrow" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          <path d="M4.427 7.427l3.396 3.396a.25.25 0 00.354 0l3.396-3.396A.25.25 0 0011.396 7H4.604a.25.25 0 00-.177.427z"/>
        </svg>
      </button>

      <!-- 统计信息 -->
      <div class="branch-stats">
        <div class="stat-item">
          <svg class="stat-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M9.5 3.25a2.25 2.25 0 1 1 3 2.122V6A2.5 2.5 0 0 1 10 8.5H6a1 1 0 0 0-1 1v1.128a2.251 2.251 0 1 1-1.5 0V5.372a2.25 2.25 0 1 1 1.5 0v1.836A2.493 2.493 0 0 1 6 7h4a1 1 0 0 0 1-1v-.628A2.25 2.25 0 0 1 9.5 3.25Zm-6 0a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0Zm8.25-.75a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5ZM4.25 12a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5Z"/>
          </svg>
          <span>{{ branchStats.branchCount }} Branches</span>
        </div>
        <div class="stat-item">
          <svg class="stat-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M1 7.775V2.75C1 1.784 1.784 1 2.75 1h5.025c.464 0 .91.184 1.238.513l3.474 3.474c.329.328.513.774.513 1.238v8.025A1.75 1.75 0 0 1 11.25 16H2.75A1.75 1.75 0 0 1 1 14.25V7.775Zm1.5 0V14.25c0 .138.112.25.25.25h8.5a.25.25 0 0 0 .25-.25V6.5H8.75A1.75 1.75 0 0 1 7 4.75V2.5H2.75a.25.25 0 0 0-.25.25v5.025Zm7-2.525V2.5l2.75 2.75H8.5Z"/>
          </svg>
          <span>{{ branchStats.tagCount }} Tags</span>
        </div>
      </div>
    </div>

    <!-- GitHub 风格的下拉菜单 -->
    <div v-if="isOpen" class="dropdown-menu" @click.stop>
      <div class="dropdown-header">
        <h3 class="dropdown-title">Switch branches/tags</h3>
        <button class="close-button" @click="closeDropdown">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.75.75 0 1 1 1.06 1.06L9.06 8l3.22 3.22a.75.75 0 1 1-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 0 1-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06Z"/>
          </svg>
        </button>
      </div>

      <!-- 搜索框 -->
      <div class="search-container">
        <div class="search-input-wrapper">
          <svg class="search-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M10.68 11.74a6 6 0 0 1-7.922-8.982 6 6 0 0 1 8.982 7.922l3.04 3.04a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215ZM11.5 7a4.499 4.499 0 1 0-8.997 0A4.499 4.499 0 0 0 11.5 7Z"/>
          </svg>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Find a branch..."
            class="search-input"
          />
        </div>
      </div>

      <!-- 标签页 -->
      <div class="tab-container">
        <button
          class="tab-button"
          :class="{ active: activeTab === 'branches' }"
          @click="activeTab = 'branches'"
        >
          Branches
        </button>
        <button
          class="tab-button"
          :class="{ active: activeTab === 'tags' }"
          @click="activeTab = 'tags'"
        >
          Tags
        </button>
      </div>

      <div class="dropdown-content">
        <!-- 分支列表 -->
        <div v-if="activeTab === 'branches'" class="branch-list">
          <div
            v-for="branch in filteredBranches"
            :key="branch.id"
            class="branch-item"
            :class="{ active: branch.id === selectedBranch.id }"
            @click="selectBranch(branch)"
          >
            <div class="branch-item-content">
              <svg v-if="branch.id === selectedBranch.id" class="check-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M13.78 4.22a.75.75 0 0 1 0 1.06l-7.25 7.25a.75.75 0 0 1-1.06 0L2.22 9.28a.75.75 0 0 1 1.06-1.06L6 10.94l6.72-6.72a.75.75 0 0 1 1.06 0Z"/>
              </svg>
              <span class="branch-name">{{ branch.name }}</span>
              <span v-if="branch.isDefault" class="default-badge">default</span>
            </div>
          </div>
        </div>

        <!-- 标签列表 -->
        <div v-if="activeTab === 'tags'" class="tag-list">
          <div
            v-for="tag in filteredTags"
            :key="tag.id"
            class="tag-item"
            @click="selectTag(tag)"
          >
            <div class="tag-item-content">
              <span class="tag-name">{{ tag.name }}</span>
              <span v-if="tag.version" class="version-badge">{{ tag.version }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部链接 -->
      <div class="dropdown-footer">
        <button class="view-all-link" @click="viewAllBranches">
          View all branches
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// Props
const props = defineProps({
  branches: {
    type: Array,
    default: () => []
  },
  tags: {
    type: Array,
    default: () => []
  },
  branchStats: {
    type: Object,
    default: () => ({ branchCount: 0, tagCount: 0 })
  }
});

// Emits
const emit = defineEmits(['branch-changed', 'tag-changed', 'view-all-branches']);

// State
const isOpen = ref(false);
const selectedBranch = ref(props.branches[0] || { id: 1, name: 'master', isDefault: true });
const searchQuery = ref('');
const activeTab = ref('branches');

// Computed
const filteredBranches = computed(() => {
  if (!searchQuery.value) return props.branches;
  return props.branches.filter(branch =>
    branch.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

const filteredTags = computed(() => {
  if (!props.tags) return [];
  if (!searchQuery.value) return props.tags;
  return props.tags.filter(tag =>
    tag.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

// Methods
const toggleDropdown = () => {
  isOpen.value = !isOpen.value;
  if (isOpen.value) {
    searchQuery.value = '';
    activeTab.value = 'branches';
  }
};

const closeDropdown = () => {
  isOpen.value = false;
  searchQuery.value = '';
};

const selectBranch = (branch) => {
  selectedBranch.value = branch;
  emit('branch-changed', branch);
  closeDropdown();
};

const selectTag = (tag) => {
  emit('tag-changed', tag);
  closeDropdown();
};

const viewAllBranches = () => {
  emit('view-all-branches');
  closeDropdown();
};
</script>

<style scoped>
.github-branch-selector {
  position: relative;
  display: inline-block;
}

.branch-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.branch-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f6f8fa;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  font-size: 14px;
  color: #24292f;
  cursor: pointer;
  transition: all 0.2s;
}

.branch-button:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.branch-button.active {
  background: #f9fafb;
  border-color: #9ca3af;
}

.branch-name {
  flex: 1;
  text-align: left;
}

.dropdown-arrow {
  color: #656d76;
  flex-shrink: 0;
}

/* 统计信息 */
.branch-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #656d76;
  font-size: 14px;
}

.stat-icon {
  color: #656d76;
}

/* 下拉菜单 */
.dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  z-index: 1000;
  width: 320px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(100px);
  border: 1px solid #d0d7de;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 16px 8px 16px;
  border-bottom: 1px solid #d0d7de;
}

.dropdown-title {
  font-size: 14px;
  font-weight: 600;
  color: #24292f;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: #656d76;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.close-button:hover {
  background: #f3f4f6;
}

/* 搜索框样式 */
.search-container {
  padding: 8px 16px;
  border-bottom: 1px solid #d0d7de;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #656d76;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  font-size: 14px;
  background: #f6f8fa;
  color: #24292f;
  outline: none;
  transition: all 0.2s;
}

.search-input:focus {
  border-color: #0969da;
  background: white;
  box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.1);
}

.search-input::placeholder {
  color: #656d76;
}

/* 标签页样式 */
.tab-container {
  display: flex;
  border-bottom: 1px solid #d0d7de;
  background: #f6f8fa;
}

.tab-button {
  flex: 1;
  padding: 12px 16px;
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: #656d76;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-button:hover {
  color: #24292f;
  background: rgba(255, 255, 255, 0.5);
}

.tab-button.active {
  color: #24292f;
  border-bottom-color: #fd7e14;
  background: white;
}

.dropdown-content {
  max-height: 300px;
  overflow-y: auto;
}

.list-title {
  font-size: 12px;
  font-weight: 600;
  color: #656d76;
  text-transform: uppercase;
  margin: 16px 16px 8px 16px;
}

.branch-item,
.tag-item {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;
  background: none;
}

.branch-item:hover,
.tag-item:hover {
  background: #f6f8fa;
}

.branch-item.active {
  background: #f6f8fa;
}

.branch-item-content,
.tag-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.check-icon {
  color: #1f883d;
  flex-shrink: 0;
}

.branch-name,
.tag-name {
  flex: 1;
  font-size: 14px;
  color: #24292f;
  text-align: left;
}

.default-badge,
.version-badge {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 12px;
  background: #dbeafe;
  color: #1e40af;
  margin-left: auto;
  flex-shrink: 0;
}

/* 底部链接样式 */
.dropdown-footer {
  border-top: 1px solid #d0d7de;
  padding: 8px;
}

.view-all-link {
  display: block;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  color: #0969da;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.view-all-link:hover {
  background: #f6f8fa;
}

/* 响应式 */
@media (max-width: 768px) {
  .branch-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .branch-stats {
    justify-content: space-between;
  }
  
  .dropdown-menu {
    width: 280px;
  }
}
</style>
