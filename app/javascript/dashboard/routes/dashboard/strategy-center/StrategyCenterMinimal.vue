<template>
  <div class="strategy-center-minimal">
    <div class="header">
      <h1>策略中心</h1>
      <p>GitHub 风格分支选择器演示</p>
    </div>
    
    <div class="content">
      <!-- GitHub 分支选择器 -->
      <div class="branch-section">
        <h2>分支选择器</h2>
        <GitHubBranchSelector
          :branches="branches"
          :tags="tags"
          :branch-stats="branchStats"
          @branch-changed="handleBranchChange"
          @view-all-branches="handleViewAllBranches"
        />
      </div>
      
      <!-- 事件日志 -->
      <div class="log-section">
        <h3>事件日志</h3>
        <div class="log-content">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            {{ log }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import GitHubBranchSelector from './components/GitHubBranchSelector.vue';

// 数据
const branches = ref([
  { id: 1, name: 'master', isDefault: true },
  { id: 2, name: 'develop', isDefault: false },
  { id: 3, name: 'feature/test', isDefault: false }
]);

const tags = ref([
  { id: 1, name: 'v1.0.0', version: 'Latest' },
  { id: 2, name: 'v0.9.0', version: '' }
]);

const branchStats = ref({
  branchCount: 3,
  tagCount: 2
});

const logs = ref(['页面已加载']);

// 事件处理
const handleBranchChange = (branch) => {
  logs.value.unshift(`分支切换: ${branch.name} (${new Date().toLocaleTimeString()})`);
  console.log('Branch changed:', branch);
};

const handleViewAllBranches = () => {
  logs.value.unshift(`点击查看全部分支 (${new Date().toLocaleTimeString()})`);
  console.log('View all branches clicked');
};
</script>

<style scoped>
.strategy-center-minimal {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  margin-bottom: 2rem;
  text-align: center;
}

.header h1 {
  font-size: 2rem;
  color: #24292f;
  margin-bottom: 0.5rem;
}

.header p {
  color: #656d76;
  font-size: 1.1rem;
}

.content {
  display: grid;
  gap: 2rem;
}

.branch-section {
  padding: 1.5rem;
  border: 1px solid #d0d7de;
  border-radius: 8px;
  background: white;
}

.branch-section h2 {
  margin: 0 0 1rem 0;
  color: #24292f;
  font-size: 1.25rem;
}

.log-section {
  padding: 1.5rem;
  border: 1px solid #d0d7de;
  border-radius: 8px;
  background: #f6f8fa;
}

.log-section h3 {
  margin: 0 0 1rem 0;
  color: #24292f;
  font-size: 1.1rem;
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  padding: 0.5rem 0;
  border-bottom: 1px solid #d0d7de;
  font-size: 0.9rem;
  color: #24292f;
}

.log-item:last-child {
  border-bottom: none;
}
</style>
