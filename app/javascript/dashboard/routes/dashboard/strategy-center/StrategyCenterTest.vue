<template>
  <div class="strategy-center-test">
    <h1>策略中心测试页面</h1>
    <p>如果你能看到这个页面，说明路由和基本组件加载正常。</p>
    
    <div class="test-section">
      <h2>GitHub 分支选择器测试</h2>
      <GitHubBranchSelector
        :branches="testBranches"
        :tags="testTags"
        :branch-stats="testStats"
        @branch-changed="handleBranchChange"
        @view-all-branches="handleViewAllBranches"
      />
    </div>
    
    <div class="debug-info">
      <h3>调试信息</h3>
      <pre>{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import GitHubBranchSelector from './components/GitHubBranchSelector.vue';

const testBranches = ref([
  { id: 1, name: 'master', isDefault: true },
  { id: 2, name: 'develop', isDefault: false },
  { id: 3, name: 'feature/test', isDefault: false }
]);

const testTags = ref([
  { id: 1, name: 'v1.0.0', version: 'Latest' },
  { id: 2, name: 'v0.9.0', version: '' }
]);

const testStats = ref({
  branchCount: 3,
  tagCount: 2
});

const debugInfo = ref({
  branches: testBranches.value,
  tags: testTags.value,
  stats: testStats.value,
  timestamp: new Date().toISOString()
});

const handleBranchChange = (branch) => {
  console.log('Branch changed:', branch);
  debugInfo.value.lastBranchChange = {
    branch: branch,
    time: new Date().toISOString()
  };
};

const handleViewAllBranches = () => {
  console.log('View all branches clicked');
  debugInfo.value.lastAction = {
    action: 'view-all-branches',
    time: new Date().toISOString()
  };
};
</script>

<style scoped>
.strategy-center-test {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin: 2rem 0;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.debug-info {
  margin-top: 2rem;
  padding: 1rem;
  background: #f5f5f5;
  border-radius: 8px;
}

.debug-info pre {
  background: white;
  padding: 1rem;
  border-radius: 4px;
  overflow: auto;
}

h1 {
  color: #333;
  margin-bottom: 1rem;
}

h2 {
  color: #666;
  margin-bottom: 1rem;
}

h3 {
  color: #888;
  margin-bottom: 0.5rem;
}
</style>
