<template>
  <div class="demo-container">
    <div class="demo-header">
      <h1 class="demo-title">GitHub 风格分支选择器演示</h1>
      <p class="demo-description">
        完全参考 GitHub 仓库的分支选择器设计，包括搜索、标签页切换、底部操作按钮等功能。
      </p>
    </div>

    <div class="demo-content">
      <!-- 原始 GitHub 界面参考 -->
      <div class="reference-section">
        <h2 class="section-title">参考设计</h2>
        <div class="reference-images">
          <div class="reference-item">
            <h3>GitHub 分支选择器 - 关闭状态</h3>
            <div class="github-mockup closed">
              <div class="github-header">
                <button class="github-branch-btn">
                  <svg width="16" height="16" fill="currentColor">
                    <path d="M9.5 3.25a2.25 2.25 0 1 1 3 2.122V6A2.5 2.5 0 0 1 10 8.5H6a1 1 0 0 0-1 1v1.128a2.251 2.251 0 1 1-1.5 0V5.372a2.25 2.25 0 1 1 1.5 0v1.836A2.493 2.493 0 0 1 6 7h4a1 1 0 0 0 1-1v-.628A2.25 2.25 0 0 1 9.5 3.25Zm-6 0a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0Zm8.25-.75a.75.75 0 1 0 0 ********* 0 0 0 0-1.5ZM4.25 12a.75.75 0 1 0 0 ********* 0 0 0 0-1.5Z"/>
                  </svg>
                  master
                  <svg width="16" height="16" fill="currentColor">
                    <path d="M4.427 7.427l3.396 3.396a.25.25 0 00.354 0l3.396-3.396A.25.25 0 0011.396 7H4.604a.25.25 0 00-.177.427z"/>
                  </svg>
                </button>
                <div class="github-stats">
                  <span class="stat">
                    <svg width="16" height="16" fill="currentColor">
                      <path d="M9.5 3.25a2.25 2.25 0 1 1 3 2.122V6A2.5 2.5 0 0 1 10 8.5H6a1 1 0 0 0-1 1v1.128a2.251 2.251 0 1 1-1.5 0V5.372a2.25 2.25 0 1 1 1.5 0v1.836A2.493 2.493 0 0 1 6 7h4a1 1 0 0 0 1-1v-.628A2.25 2.25 0 0 1 9.5 3.25Zm-6 0a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0Zm8.25-.75a.75.75 0 1 0 0 ********* 0 0 0 0-1.5ZM4.25 12a.75.75 0 1 0 0 ********* 0 0 0 0-1.5Z"/>
                    </svg>
                    197 Branches
                  </span>
                  <span class="stat">
                    <svg width="16" height="16" fill="currentColor">
                      <path d="M1 7.775V2.75C1 1.784 1.784 1 2.75 1h5.025c.464 0 .91.184 1.238.513l3.474 3.474c.329.328.513.774.513 1.238v8.025A1.75 1.75 0 0 1 11.25 16H2.75A1.75 1.75 0 0 1 1 14.25V7.775Zm1.5 0V14.25c0 .138.112.25.25.25h8.5a.25.25 0 0 0 .25-.25V6.5H8.75A1.75 1.75 0 0 1 7 4.75V2.5H2.75a.25.25 0 0 0-.25.25v5.025Zm7-2.525V2.5l2.75 2.75H8.5Z"/>
                    </svg>
                    341 Tags
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实际组件演示 -->
      <div class="implementation-section">
        <h2 class="section-title">组件实现</h2>
        <div class="demo-wrapper">
          <GitHubBranchSelector
            :branches="demoData.branches"
            :tags="demoData.tags"
            :branch-stats="demoData.branchStats"
            @branch-changed="handleBranchChange"
            @view-all-branches="handleViewAllBranches"
          />
        </div>
        
        <!-- 事件日志 -->
        <div class="event-log">
          <h3>事件日志</h3>
          <div class="log-content">
            <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-event">{{ log.event }}</span>
              <span class="log-data">{{ log.data }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能特性说明 -->
      <div class="features-section">
        <h2 class="section-title">功能特性</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🔍</div>
            <h3>实时搜索</h3>
            <p>支持分支和标签的实时搜索过滤</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📑</div>
            <h3>标签页切换</h3>
            <p>Branches 和 Tags 标签页无缝切换</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">✅</div>
            <h3>选中状态</h3>
            <p>清晰的选中状态指示和默认分支标识</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🎨</div>
            <h3>GitHub 风格</h3>
            <p>完全还原 GitHub 的视觉设计和交互</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">⌨️</div>
            <h3>键盘导航</h3>
            <p>支持键盘操作和无障碍访问</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🔗</div>
            <h3>底部操作</h3>
            <p>"View all branches" 按钮支持跳转</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import GitHubBranchSelector from './GitHubBranchSelector.vue';

// 演示数据
const demoData = {
  branches: [
    { id: 1, name: 'master', isDefault: true },
    { id: 2, name: 'pb/cli-fix-devbot', isDefault: false },
    { id: 3, name: 're-integrations-trello', isDefault: false },
    { id: 4, name: 'rk-zendesk', isDefault: false },
    { id: 5, name: 'sp/bump-sdk', isDefault: false },
    { id: 6, name: 'sp/charts-bump', isDefault: false },
    { id: 7, name: 'sp/choice-no-text', isDefault: false },
    { id: 8, name: 'sp/choice-no-text-2', isDefault: false },
    { id: 9, name: 'sp/deploy-charts-and-browser', isDefault: false },
    { id: 10, name: 'sp/fix-cognitive-types', isDefault: false },
  ],
  tags: [
    { id: 1, name: 'v2.1.0', version: 'Latest' },
    { id: 2, name: 'v2.0.5', version: '' },
    { id: 3, name: 'v2.0.4', version: '' },
    { id: 4, name: 'v2.0.3', version: '' },
    { id: 5, name: 'v2.0.2', version: '' },
  ],
  branchStats: {
    branchCount: 197,
    tagCount: 341
  }
};

// 事件日志
const eventLogs = ref([]);

// 事件处理
const handleBranchChange = (branch) => {
  addLog('分支切换', `切换到分支: ${branch.name}`);
};

const handleViewAllBranches = () => {
  addLog('查看全部', '点击了 "View all branches" 按钮');
};

const addLog = (event, data) => {
  const now = new Date();
  const time = now.toLocaleTimeString();
  eventLogs.value.unshift({ time, event, data });
  
  // 只保留最近 10 条日志
  if (eventLogs.value.length > 10) {
    eventLogs.value = eventLogs.value.slice(0, 10);
  }
};
</script>

<style scoped>
.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.demo-header {
  text-align: center;
  margin-bottom: 3rem;
}

.demo-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #24292f;
  margin-bottom: 1rem;
}

.demo-description {
  font-size: 1.125rem;
  color: #656d76;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #24292f;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #d0d7de;
  padding-bottom: 0.5rem;
}

/* 参考设计部分 */
.reference-section {
  margin-bottom: 3rem;
}

.github-mockup {
  border: 1px solid #d0d7de;
  border-radius: 12px;
  padding: 1rem;
  background: #f6f8fa;
}

.github-header {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.github-branch-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 6px 12px;
  background: #f6f8fa;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #24292f;
}

.github-stats {
  display: flex;
  gap: 1rem;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #656d76;
  font-size: 14px;
}

/* 实现部分 */
.implementation-section {
  margin-bottom: 3rem;
}

.demo-wrapper {
  padding: 2rem;
  border: 1px solid #d0d7de;
  border-radius: 12px;
  background: #ffffff;
  margin-bottom: 2rem;
}

/* 事件日志 */
.event-log {
  background: #f6f8fa;
  border: 1px solid #d0d7de;
  border-radius: 8px;
  padding: 1rem;
}

.event-log h3 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #24292f;
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  gap: 1rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #d0d7de;
  font-size: 0.875rem;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #656d76;
  min-width: 80px;
}

.log-event {
  color: #0969da;
  font-weight: 500;
  min-width: 80px;
}

.log-data {
  color: #24292f;
}

/* 功能特性 */
.features-section {
  margin-bottom: 3rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  background: #ffffff;
  border: 1px solid #d0d7de;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
}

.feature-card:hover {
  border-color: #0969da;
  box-shadow: 0 4px 12px rgba(9, 105, 218, 0.1);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #24292f;
  margin-bottom: 0.5rem;
}

.feature-card p {
  color: #656d76;
  line-height: 1.5;
  margin: 0;
}
</style>
