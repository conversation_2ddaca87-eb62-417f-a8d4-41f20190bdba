<script setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  Listbox,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from '@headlessui/vue';
import Button from 'dashboard/components-next/button/Button.vue';
import Icon from 'dashboard/components-next/icon/Icon.vue';
import Badge from 'dashboard/components-next/badge/Badge.vue';
import GitHubBranchSelector from './components/GitHubBranchSelector.vue';

const { t } = useI18n();

// 模拟数据
const userOptions = computed(() => [
  { value: 1, label: '<PERSON>', name: '<PERSON>' },
  { value: 2, label: '<PERSON><PERSON>', name: '<PERSON><PERSON>' },
  { value: 3, label: '<PERSON>', name: '<PERSON>' }
]);

const selectedUser = ref({ value: 1, label: '<PERSON>', name: '<PERSON>' });

const branches = computed(() => [
  { id: 1, name: 'master', isDefault: true },
  { id: 2, name: 'develop', isDefault: false },
  { id: 3, name: 'feature/strategy-v2', isDefault: false },
  { id: 4, name: 'pb/cli-fix-devbot', isDefault: false },
  { id: 5, name: 're-integrations-trello', isDefault: false },
  { id: 6, name: 'rk-zendesk', isDefault: false },
  { id: 7, name: 'sp/bump-sdk', isDefault: false },
  { id: 8, name: 'sp/charts-bump', isDefault: false },
  { id: 9, name: 'sp/choice-no-text', isDefault: false },
  { id: 10, name: 'sp/choice-no-text-2', isDefault: false },
]);

const tags = computed(() => [
  { id: 1, name: 'v2.1.0', version: 'Latest' },
  { id: 2, name: 'v2.0.5', version: '' },
  { id: 3, name: 'v2.0.4', version: '' },
  { id: 4, name: 'v2.0.3', version: '' },
  { id: 5, name: 'v2.0.2', version: '' },
]);

const selectedBranch = ref({ id: 1, name: 'master', isDefault: true });

const branchStats = ref({
  branchCount: 197,
  tagCount: 341
});

// 策略表格数据
const strategyRows = ref([
  {
    id: 1,
    stage: '引入期',
    trigger: '初次接触',
    target: '吸引用户，引导完成关键行为',
    strategy: '欢迎SOP（自动介绍）<br>• 新人大礼包（介绍福利、品牌价值）<br>• 发放新人专享福利<br>• 营销内容（自动介绍、小贴士）',
    agent: '获客专家',
    importance: '正常介绍',
    constraints: '如果用户介绍策略基本相同，避免不断重复介绍用户，必须等客户再次进行互动'
  },
  {
    id: 2,
    stage: '成长期',
    trigger: '互动超过10次',
    target: '培养用户使用习惯，推进转化',
    strategy: '• 每日干货分享或活动讯息<br>• 使用指导（体验、用法）<br>• 1对1私信，按需分享',
    agent: '互动专家',
    importance: '正常介绍',
    constraints: '如果用户介绍策略基本相同，避免不断重复介绍用户，必须等客户再次进行互动'
  },
  {
    id: 3,
    stage: '成熟期',
    trigger: '用户活跃',
    target: '提升用户忠诚度，最大化用户价值',
    strategy: '• VIP会员体系，提供专属权益<br>• 新品优先体验<br>• 建立用户群（KOC群）<br>• 深度互动/合作',
    agent: '转化专家',
    importance: '正常介绍',
    constraints: '如果用户介绍策略基本相同，避免不断重复介绍用户，必须等客户再次进行互动'
  },
  {
    id: 4,
    stage: '衰退期',
    trigger: '超过7天未互动',
    target: '识别流失风险，主动干预，唤醒用户',
    strategy: '• 自动化推送（识别数据异常）<br>• 红包SOP（私域支持、问候语）<br>• 大额优惠券<br>• 限时利益刺激<br>• 用户调研，了解不活跃原因',
    agent: '关怀专家',
    importance: '徐水活跃',
    constraints: '如果用户介绍策略基本相同，避免不断重复介绍用户，必须等客户再次进行互动'
  },
  {
    id: 5,
    stage: '流失期',
    trigger: '超过15天未互动',
    target: '分析流失原因，指导产品优化，营销策略调整',
    strategy: '• 进行流失用户访谈（如果可行）<br>• 分析流失原因，适时更新策略<br>• 和新产品上线时，适当推送<br>• 优化营销策略，防止更多用户流失',
    agent: '召回专家',
    importance: '徐水活跃',
    constraints: '如果用户介绍策略基本相同，避免不断重复介绍用户，必须等客户再次进行互动'
  }
]);



// 事件处理
const handleUserChange = (user) => {
  console.log('User changed:', user);
};

const handleCreateTask = () => {
  console.log('Create task clicked');
};

const handleBranchChange = (branch) => {
  console.log('Branch changed:', branch);
};

const handleViewAllBranches = () => {
  console.log('View all branches clicked');
  // 这里可以导航到分支列表页面
};

const handleDataBranch = () => {
  console.log('Data branch clicked');
};

const handleAddRow = () => {
  const newRow = {
    id: Date.now(),
    stage: '新阶段',
    trigger: '',
    target: '',
    strategy: '',
    agent: '',
    importance: '',
    constraints: ''
  };
  strategyRows.value.push(newRow);
};
</script>

<template>
  <section class="flex flex-col w-full h-full overflow-hidden bg-n-background">
    <main class="flex-1 px-6 overflow-y-auto">
      <div class="w-full max-w-[60rem] mx-auto py-6">
        <div class="strategy-center">
          <!-- 顶部工具栏 -->
          <div class="strategy-header">
            <div class="header-left">
              <!-- 用户选择器 - 使用 Headless UI Listbox -->
              <div class="user-selector">
                <Listbox v-model="selectedUser" @update:model-value="handleUserChange">
                  <div class="relative">
                    <ListboxButton class="user-select">
                      <span class="block truncate">{{ selectedUser.name }}</span>
                      <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                        <svg class="select-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                      </span>
                    </ListboxButton>

              <transition
                leave-active-class="transition duration-100 ease-in"
                leave-from-class="opacity-100"
                leave-to-class="opacity-0"
              >
                <ListboxOptions class="user-options">
                  <ListboxOption
                    v-slot="{ active, selected }"
                    v-for="user in userOptions"
                    :key="user.value"
                    :value="user"
                    as="template"
                  >
                    <li
                      :class="[
                        active ? 'bg-blue-100 text-blue-900' : 'text-gray-900',
                        'relative cursor-default select-none py-2 pl-10 pr-4',
                      ]"
                    >
                      <span
                        :class="[
                          selected ? 'font-medium' : 'font-normal',
                          'block truncate',
                        ]"
                      >
                        {{ user.label }}
                      </span>
                      <span
                        v-if="selected"
                        class="absolute inset-y-0 left-0 flex items-center pl-3 text-blue-600"
                      >
                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                      </span>
                    </li>
                  </ListboxOption>
                </ListboxOptions>
              </transition>
            </div>
          </Listbox>
        </div>
      </div>

      <div class="header-right">
        <button
          class="create-task-btn"
          @click="handleCreateTask"
        >
          新建任务
        </button>
      </div>
    </div>

    <!-- 分支信息栏 - GitHub 风格，顶部并排布局 -->
    <div class="branch-info">
      <div class="branch-selectors">
        <!-- 主分支选择器 -->
        <GitHubBranchSelector
          :branches="branches"
          :tags="tags"
          :branch-stats="branchStats"
          @branch-changed="handleBranchChange"
          @view-all-branches="handleViewAllBranches"
        />

        <!-- 分支统计按钮 -->
        <div class="branch-stats-buttons">
          <button class="stat-button">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M9.5 3.25a2.25 2.25 0 1 1 3 2.122V6A2.5 2.5 0 0 1 10 8.5H6a1 1 0 0 0-1 1v1.128a2.251 2.251 0 1 1-1.5 0V5.372a2.25 2.25 0 1 1 1.5 0v1.836A2.493 2.493 0 0 1 6 7h4a1 1 0 0 0 1-1v-.628A2.25 2.25 0 0 1 9.5 3.25Zm-6 0a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0Zm8.25-.75a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5ZM4.25 12a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5Z"/>
            </svg>
            <span>198 Branches</span>
            <svg class="dropdown-arrow" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.94l3.72-3.72a.749.749 0 0 1 1.06 0Z"/>
            </svg>
          </button>

          <button class="stat-button">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M1 7.775V2.75C1 1.784 1.784 1 2.75 1h5.025c.464 0 .91.184 1.238.513l3.474 3.474c.329.328.513.774.513 1.238v8.025A1.75 1.75 0 0 1 11.25 16H2.75A1.75 1.75 0 0 1 1 14.25V7.775Z"/>
            </svg>
            <span>341 Tags</span>
            <svg class="dropdown-arrow" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.94l3.72-3.72a.749.749 0 0 1 1.06 0Z"/>
            </svg>
          </button>
        </div>
      </div>

      <div class="branch-actions">
        <button
          class="data-branch-btn"
          @click="handleDataBranch"
        >
          数据分支
        </button>
      </div>
    </div>

    <!-- 策略表格 -->
    <div class="strategy-table-container">
      <div class="table-header">
        <div class="header-tabs">
          <div class="tab active">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
            生命周期阶段
          </div>
          <div class="tab">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"></polygon>
            </svg>
            触发
          </div>
          <div class="tab">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <circle cx="12" cy="12" r="6"></circle>
              <circle cx="12" cy="12" r="2"></circle>
            </svg>
            运营目标
          </div>
          <div class="tab">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
            </svg>
            运营策略
          </div>
          <div class="tab">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            agent
          </div>
          <div class="tab">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
            重要性
          </div>
          <div class="tab">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
              <circle cx="12" cy="16" r="1"></circle>
              <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
            </svg>
            限制条件
          </div>
        </div>
      </div>

      <div class="strategy-table">
        <div
          v-for="(row, index) in strategyRows"
          :key="row.id"
          class="table-row"
          :class="{ 'row-even': index % 2 === 1 }"
        >
          <div class="cell stage-cell">{{ row.stage }}</div>
          <div class="cell trigger-cell">{{ row.trigger }}</div>
          <div class="cell target-cell">{{ row.target }}</div>
          <div class="cell strategy-cell" v-html="row.strategy"></div>
          <div class="cell agent-cell">
            <span class="agent-badge">{{ row.agent }}</span>
          </div>
          <div class="cell importance-cell">
            <span class="importance-badge">{{ row.importance }}</span>
          </div>
          <div class="cell constraints-cell">{{ row.constraints }}</div>
        </div>

        <!-- 添加新行按钮 -->
        <div class="add-row-container">
          <button
            class="add-row-btn"
            @click="handleAddRow"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            新增策略
          </button>
        </div>
      </div>
        </div>
      </div>
    </main>
  </section>
</template>

<style scoped>
.strategy-center {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 顶部工具栏 */
.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;
}

@media (max-width: 768px) {
  .strategy-header {
    flex-direction: column;
    align-items: stretch;
  }
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .header-left {
    width: 100%;
    justify-content: space-between;
  }
}

.user-selector {
  position: relative;
  display: flex;
  align-items: center;
}

.user-select {
  appearance: none;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px 32px 8px 12px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  min-width: 160px;
  position: relative;
  width: 100%;
  text-align: left;
}

.user-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.select-icon {
  color: #6b7280;
  pointer-events: none;
}

.user-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 10;
  margin-top: 4px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  max-height: 200px;
  overflow-y: auto;
}

.create-task-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.create-task-btn:hover {
  background: #2563eb;
}

/* 分支信息栏 */
.branch-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
}

/* 分支选择器组 */
.branch-selectors {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 分支统计按钮组 */
.branch-stats-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f6f8fa;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  font-size: 14px;
  color: #24292f;
  cursor: pointer;
  transition: all 0.2s;
}

.stat-button:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.stat-button svg {
  flex-shrink: 0;
}

.stat-button .dropdown-arrow {
  color: #656d76;
  margin-left: 2px;
}

.branch-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.branch-selector {
  position: relative;
}

.branch-select-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  background: transparent;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.branch-select-btn:hover {
  background: #f3f4f6;
}

.branch-name {
  font-size: 14px;
}

.branch-options {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 10;
  margin-top: 4px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  min-width: 200px;
  max-height: 200px;
  overflow-y: auto;
}

.stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  font-size: 13px;
}

.data-branch-btn {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 13px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
}

.data-branch-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

/* 策略表格容器 */
.strategy-table-container {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

@media (max-width: 768px) {
  .strategy-table-container {
    overflow-x: auto;
  }
}

.table-header {
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.header-tabs {
  display: grid;
  grid-template-columns: 100px 120px 200px 300px 100px 100px 1fr;
  border-bottom: 1px solid #e5e7eb;
}

.tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 12px 16px;
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
  border-right: 1px solid #e5e7eb;
  background: #f9fafb;
}

.tab.active {
  color: #374151;
  background: white;
}

.tab:last-child {
  border-right: none;
}

/* 策略表格 */
.strategy-table {
  max-height: 600px;
  overflow-y: auto;
  min-width: 800px; /* 确保表格有最小宽度 */
}

@media (max-width: 768px) {
  .strategy-table {
    min-width: 1000px; /* 移动端需要更大的最小宽度以保持可读性 */
  }
}

.table-row {
  display: grid;
  grid-template-columns: 100px 120px 200px 300px 100px 100px 1fr;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.1s;
}

.table-row:hover {
  background: #f9fafb;
}

.table-row.row-even {
  background: #fafafa;
}

.table-row.row-even:hover {
  background: #f3f4f6;
}

.cell {
  padding: 12px 16px;
  font-size: 13px;
  line-height: 1.4;
  border-right: 1px solid #f3f4f6;
  display: flex;
  align-items: flex-start;
  word-break: break-word;
}

.cell:last-child {
  border-right: none;
}

.stage-cell {
  font-weight: 500;
  color: #374151;
}

.trigger-cell {
  color: #6b7280;
}

.target-cell {
  color: #374151;
}

.strategy-cell {
  color: #374151;
  line-height: 1.5;
}

.agent-cell, .importance-cell {
  justify-content: center;
}

.agent-badge {
  background: #dbeafe;
  color: #1e40af;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.importance-badge {
  background: #dcfce7;
  color: #166534;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.constraints-cell {
  color: #6b7280;
  font-size: 12px;
}

/* 添加新行按钮 */
.add-row-container {
  padding: 16px;
  text-align: center;
  border-top: 1px solid #f3f4f6;
}

.add-row-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: transparent;
  border: 1px dashed #d1d5db;
  border-radius: 6px;
  padding: 8px 16px;
  color: #6b7280;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.add-row-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: #f0f9ff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-tabs,
  .table-row {
    grid-template-columns: 80px 100px 180px 250px 80px 80px 1fr;
  }

  .cell {
    padding: 10px 12px;
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .strategy-center {
    padding: 12px;
  }

  .strategy-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .branch-info {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-tabs,
  .table-row {
    display: block;
  }

  .cell {
    display: block;
    border-right: none;
    border-bottom: 1px solid #f3f4f6;
    padding: 8px 12px;
  }

  .cell:before {
    content: attr(data-label);
    font-weight: 500;
    color: #6b7280;
    display: block;
    margin-bottom: 4px;
  }
}
</style>
