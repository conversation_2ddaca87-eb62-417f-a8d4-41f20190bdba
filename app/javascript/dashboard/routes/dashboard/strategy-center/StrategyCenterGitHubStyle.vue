<template>
  <section class="flex flex-col w-full h-full overflow-hidden bg-n-background">
    <main class="flex-1 px-6 overflow-y-auto">
      <div class="w-full max-w-[60rem] mx-auto py-6">
        <div class="strategy-center">
          <!-- 顶部工具栏 -->
          <div class="strategy-header">
            <div class="header-left">
              <!-- 用户选择器 -->
              <div class="user-selector">
                <button class="user-select">
                  <span class="block truncate"><PERSON></span>
                  <svg class="select-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </button>
              </div>
            </div>

            <div class="header-right">
              <button class="create-task-btn">
                新建任务
              </button>
            </div>
          </div>

          <!-- GitHub 风格的分支信息栏 - 顶部并排布局 -->
          <div class="branch-info">
            <div class="branch-selectors">
              <!-- 主分支选择器 -->
              <GitHubBranchSelectorSimple
                :branches="branches"
                :tags="tags"
                @branch-selected="handleBranchSelected"
                @tag-selected="handleTagSelected"
                @view-all-branches="handleViewAllBranches"
              />
              
              <!-- 分支统计按钮 -->
              <div class="branch-stats-buttons">
                <button class="stat-button">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M9.5 3.25a2.25 2.25 0 1 1 3 2.122V6A2.5 2.5 0 0 1 10 8.5H6a1 1 0 0 0-1 1v1.128a2.251 2.251 0 1 1-1.5 0V5.372a2.25 2.25 0 1 1 1.5 0v1.836A2.493 2.493 0 0 1 6 7h4a1 1 0 0 0 1-1v-.628A2.25 2.25 0 0 1 9.5 3.25Zm-6 0a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0Zm8.25-.75a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5ZM4.25 12a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5Z"/>
                  </svg>
                  <span>198 Branches</span>
                  <svg class="dropdown-arrow" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.94l3.72-3.72a.749.749 0 0 1 1.06 0Z"/>
                  </svg>
                </button>
                
                <button class="stat-button">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M1 7.775V2.75C1 1.784 1.784 1 2.75 1h5.025c.464 0 .91.184 1.238.513l3.474 3.474c.329.328.513.774.513 1.238v8.025A1.75 1.75 0 0 1 11.25 16H2.75A1.75 1.75 0 0 1 1 14.25V7.775Z"/>
                  </svg>
                  <span>341 Tags</span>
                  <svg class="dropdown-arrow" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.94l3.72-3.72a.749.749 0 0 1 1.06 0Z"/>
                  </svg>
                </button>
              </div>
            </div>

            <div class="branch-actions">
              <button class="data-branch-btn">
                数据分支
              </button>
            </div>
          </div>

          <!-- 策略表格 -->
          <div class="strategy-table-container">
            <div class="table-header">
              <div class="header-tabs">
                <div class="tab active">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                  </svg>
                  生命周期阶段
                </div>
                <div class="tab">触发</div>
                <div class="tab">运营目标</div>
                <div class="tab">运营策略</div>
                <div class="tab">agent</div>
                <div class="tab">重要性</div>
                <div class="tab">限制条件</div>
              </div>
            </div>

            <!-- 表格内容 -->
            <div class="strategy-table">
              <div class="table-row header-row">
                <div class="cell">引入期</div>
                <div class="cell">初次体验</div>
                <div class="cell">引导用户，引导用户完成关键操作</div>
                <div class="cell">数据专家</div>
                <div class="cell">敏水活跃</div>
                <div class="cell">常见问题</div>
                <div class="cell">如果用户介绍策略基本相同，避免不断重复介绍用户，必须等客户再次进行互动</div>
              </div>
              
              <div class="table-row">
                <div class="cell">成长期</div>
                <div class="cell">互动频次10次</div>
                <div class="cell">提升用户粘性，推动向深度使用转化</div>
                <div class="cell">互动专家</div>
                <div class="cell">正常对话</div>
                <div class="cell">如果遇到资料问题</div>
                <div class="cell">如果用户介绍策略基本相同，避免不断重复介绍用户，必须等客户再次进行互动</div>
              </div>
              
              <div class="table-row">
                <div class="cell">成熟期</div>
                <div class="cell">用户满意</div>
                <div class="cell">提升用户满意度，最大化用户价值</div>
                <div class="cell">转化专家</div>
                <div class="cell">正常对话</div>
                <div class="cell">如果遇到资料问题</div>
                <div class="cell">如果用户介绍策略基本相同，避免不断重复介绍用户，必须等客户再次进行互动</div>
              </div>
              
              <div class="table-row">
                <div class="cell">衰退期</div>
                <div class="cell">超7天未互动</div>
                <div class="cell">识别衰退风险，采取挽回措施</div>
                <div class="cell">关怀专家</div>
                <div class="cell">正常对话</div>
                <div class="cell">如果遇到资料问题</div>
                <div class="cell">如果用户介绍策略基本相同，避免不断重复介绍用户，必须等客户再次进行互动</div>
              </div>
              
              <div class="table-row">
                <div class="cell">流失期</div>
                <div class="cell">超15天未互动</div>
                <div class="cell">分析流失原因，指导产品优化，营销策略调整</div>
                <div class="cell">召回专家</div>
                <div class="cell">徐水活跃</div>
                <div class="cell">常见问题</div>
                <div class="cell">如果用户介绍策略基本相同，避免不断重复介绍用户，必须等客户再次进行互动</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </section>
</template>

<script setup>
import { ref, computed } from 'vue';
import GitHubBranchSelectorSimple from './components/GitHubBranchSelectorSimple.vue';

// 模拟数据
const branches = computed(() => [
  { id: 1, name: 'master', isDefault: true },
  { id: 2, name: 'cls-1335-integrationwa-user-should-pr...', isDefault: false },
  { id: 3, name: 're-integrations-asana', isDefault: false },
  { id: 4, name: 're-integrations-trello', isDefault: false },
  { id: 5, name: 'rk-zendesk', isDefault: false },
  { id: 6, name: 'sp/bump-sdk', isDefault: false },
  { id: 7, name: 'sp/charts-bump', isDefault: false },
  { id: 8, name: 'sp/choice-no-text', isDefault: false },
  { id: 9, name: 'sp/choice-no-text-2', isDefault: false },
  { id: 10, name: 'sp/deploy-charts-and-browser', isDefault: false },
]);

const tags = computed(() => [
  { id: 1, name: 'v2.1.0', version: 'Latest' },
  { id: 2, name: 'v2.0.5', version: '' },
  { id: 3, name: 'v2.0.4', version: '' },
  { id: 4, name: 'v2.0.3', version: '' },
  { id: 5, name: 'v2.0.2', version: '' },
]);

// 事件处理
const handleBranchSelected = (branch) => {
  console.log('Branch selected:', branch);
};

const handleTagSelected = (tag) => {
  console.log('Tag selected:', tag);
};

const handleViewAllBranches = () => {
  console.log('View all branches clicked');
};
</script>

<style scoped>
.strategy-center {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 顶部工具栏 */
.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-selector {
  position: relative;
}

.user-select {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 200px;
  padding: 8px 12px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: border-color 0.2s;
}

.user-select:hover {
  border-color: #9ca3af;
}

.select-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.create-task-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.create-task-btn:hover {
  background: #2563eb;
}

/* 分支信息栏 */
.branch-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
}

/* 分支选择器组 */
.branch-selectors {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 分支统计按钮组 */
.branch-stats-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f6f8fa;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  font-size: 14px;
  color: #24292f;
  cursor: pointer;
  transition: all 0.2s;
}

.stat-button:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.stat-button svg {
  flex-shrink: 0;
}

.stat-button .dropdown-arrow {
  color: #656d76;
  margin-left: 2px;
}

.data-branch-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.data-branch-btn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

/* 策略表格 */
.strategy-table-container {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  padding: 12px 16px;
}

.header-tabs {
  display: grid;
  grid-template-columns: 100px 120px 200px 300px 100px 100px 1fr;
  gap: 16px;
}

.tab {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.tab.active {
  color: #3b82f6;
}

.strategy-table {
  max-height: 600px;
  overflow-y: auto;
}

.table-row {
  display: grid;
  grid-template-columns: 100px 120px 200px 300px 100px 100px 1fr;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.1s;
}

.table-row:hover {
  background: #f9fafb;
}

.cell {
  padding: 12px 16px;
  font-size: 13px;
  line-height: 1.4;
  border-right: 1px solid #f3f4f6;
  display: flex;
  align-items: flex-start;
  word-break: break-word;
}

.cell:last-child {
  border-right: none;
}

/* 响应式 */
@media (max-width: 768px) {
  .strategy-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .branch-info {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .branch-selectors {
    justify-content: space-between;
  }

  .strategy-table {
    min-width: 1000px;
  }
}
</style>
