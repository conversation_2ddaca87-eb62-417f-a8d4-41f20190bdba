{"name": "@chatwoot/chatwoot", "version": "4.5.1", "license": "MIT", "scripts": {"eslint": "eslint app/**/*.{js,vue}", "eslint:fix": "eslint app/**/*.{js,vue} --fix", "test": "TZ=UTC vitest --no-watch --no-cache --no-coverage --logHeapUsage", "test:watch": "TZ=UTC vitest --no-cache --no-coverage", "test:coverage": "TZ=UTC vitest --no-watch --no-cache --coverage", "start:dev": "foreman start -f ./Procfile.dev", "start:test": "RAILS_ENV=test foreman start -f ./Procfile.test", "dev": "overmind start -f ./Procfile.dev", "ruby:prettier": "bundle exec rubocop -a", "build:sdk": "BUILD_MODE=library vite build", "prepare": "husky install", "size": "size-limit", "story:dev": "histoire dev", "story:build": "histoire build", "story:preview": "histoire preview", "sync:i18n": "bin/sync_i18n_file_change"}, "size-limit": [{"path": "public/vite/assets/widget-*.js", "limit": "300 KB"}, {"path": "public/packs/js/sdk.js", "limit": "40 KB"}], "dependencies": {"@breezystack/lamejs": "^1.2.7", "@chatwoot/ninja-keys": "1.2.3", "@chatwoot/prosemirror-schema": "1.2.1", "@chatwoot/utils": "^0.0.49", "@formkit/core": "^1.6.7", "@formkit/vue": "^1.6.7", "@hcaptcha/vue3-hcaptcha": "^1.3.0", "@headlessui/vue": "^1.7.23", "@highlightjs/vue-plugin": "^2.1.0", "@iconify-json/material-symbols": "^1.2.10", "@june-so/analytics-next": "^2.0.0", "@lk77/vue3-color": "^3.0.6", "@radix-ui/colors": "^3.0.0", "@rails/actioncable": "6.1.3", "@rails/ujs": "^7.1.400", "@scmmishra/pico-search": "0.5.4", "@sentry/vue": "^8.31.0", "@sindresorhus/slugify": "2.2.1", "@tailwindcss/typography": "^0.5.15", "@tanstack/vue-table": "^8.20.5", "@vitejs/plugin-vue": "^5.1.4", "@vue/compiler-sfc": "^3.5.8", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@vueuse/components": "^12.0.0", "@vueuse/core": "^12.0.0", "activestorage": "^5.2.6", "axios": "^1.8.2", "camelcase-keys": "^9.1.3", "chart.js": "~4.4.4", "color2k": "^2.0.2", "company-email-validator": "^1.1.0", "core-js": "3.38.1", "countries-and-timezones": "^3.6.0", "date-fns": "2.21.1", "date-fns-tz": "^1.3.3", "dompurify": "3.2.4", "flag-icons": "^7.2.3", "floating-vue": "^5.2.2", "highlight.js": "^11.10.0", "idb": "^8.0.0", "js-cookie": "^3.0.5", "json-logic-js": "^2.0.5", "lettersanitizer": "^1.0.6", "libphonenumber-js": "^1.11.9", "markdown-it": "^13.0.2", "markdown-it-link-attributes": "^4.0.1", "md5": "^2.3.0", "mitt": "^3.0.1", "opus-recorder": "^8.0.5", "semver": "7.6.3", "snakecase-keys": "^8.0.1", "timezone-phone-codes": "^0.0.2", "tinykeys": "^3.0.0", "turbolinks": "^5.2.0", "urlpattern-polyfill": "^10.0.0", "video.js": "7.18.1", "videojs-record": "4.5.0", "videojs-wavesurfer": "3.8.0", "vue": "^3.5.12", "vue-chartjs": "5.3.1", "vue-datepicker-next": "^1.0.3", "vue-dompurify-html": "^5.1.0", "vue-i18n": "9.14.5", "vue-letter": "^0.2.1", "vue-multiselect": "3.1.0", "vue-router": "~4.4.5", "vue-upload-component": "^3.1.17", "vue-virtual-scroller": "^2.0.0-beta.8", "vue3-click-away": "^1.2.4", "vuedraggable": "^4.1.0", "vuex": "~4.1.0", "vuex-router-sync": "6.0.0-rc.1", "wavesurfer.js": "7.8.6"}, "devDependencies": {"@egoist/tailwindcss-icons": "^1.8.1", "@histoire/plugin-vue": "0.17.15", "@iconify-json/logos": "^1.2.3", "@iconify-json/lucide": "^1.2.11", "@iconify-json/ph": "^1.2.1", "@iconify-json/ri": "^1.2.3", "@iconify-json/teenyicons": "^1.2.1", "@intlify/eslint-plugin-vue-i18n": "^3.2.0", "@size-limit/file": "^8.2.4", "@vitest/coverage-v8": "3.0.5", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-airbnb-base": "15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-interactive": "^11.1.0", "eslint-plugin-html": "7.1.0", "eslint-plugin-import": "2.30.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-vitest-globals": "^1.5.0", "eslint-plugin-vue": "^9.28.0", "fake-indexeddb": "^6.0.0", "histoire": "0.17.15", "husky": "^7.0.0", "jsdom": "^24.1.3", "lint-staged": "14.0.1", "postcss": "^8.4.47", "postcss-preset-env": "^8.5.1", "prettier": "^3.3.3", "prosemirror-model": "^1.22.3", "size-limit": "^8.2.4", "tailwindcss": "^3.4.13", "vite": "^5.4.19", "vite-plugin-ruby": "^5.0.0", "vitest": "3.0.5"}, "engines": {"node": "23.x", "pnpm": "10.x"}, "husky": {"hooks": {"pre-push": "sh bin/validate_push"}}, "pnpm": {"overrides": {"vite-node": "2.0.1", "vite": "5.4.19", "vitest": "3.0.5"}}, "lint-staged": {"app/**/*.{js,vue}": ["eslint --fix", "git add"], "*.scss": ["scss-lint"]}, "packageManager": "pnpm@10.2.0+sha512.0d27364e0139c6aadeed65ada153135e0ca96c8da42123bd50047f961339dc7a758fc2e944b428f52be570d1bd3372455c1c65fa2e7aa0bfbf931190f9552001"}